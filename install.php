<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Font Setup Installer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .step h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .code {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        form {
            display: inline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF Font Setup Installer</h1>
        
        <?php
        $action = $_GET['action'] ?? '';
        
        if ($action === 'create_directories') {
            echo "<div class='step'>";
            echo "<h3>Creating Font Directories</h3>";
            
            $directories = [
                'fonts/dejavu-serif-ttf',
                'fonts/sakal-bharati'
            ];
            
            $success = true;
            foreach ($directories as $dir) {
                if (!is_dir($dir)) {
                    if (mkdir($dir, 0755, true)) {
                        echo "<p>✓ Created directory: <code>$dir</code></p>";
                    } else {
                        echo "<p>✗ Failed to create directory: <code>$dir</code></p>";
                        $success = false;
                    }
                } else {
                    echo "<p>✓ Directory already exists: <code>$dir</code></p>";
                }
            }
            
            if ($success) {
                echo "<div class='success'><strong>All directories created successfully!</strong></div>";
            }
            echo "</div>";
        }
        
        if ($action === 'create_composer') {
            echo "<div class='step'>";
            echo "<h3>Creating composer.json</h3>";
            
            $composerConfig = [
                'name' => 'birth-project/pdf-fonts',
                'description' => 'PDF generation with custom fonts',
                'require' => [
                    'mpdf/mpdf' => '^8.0'
                ],
                'autoload' => [
                    'files' => ['pdf_generator.php']
                ]
            ];
            
            if (file_put_contents('composer.json', json_encode($composerConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES))) {
                echo "<p>✓ Created composer.json</p>";
                echo "<div class='code'>" . json_encode($composerConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "</div>";
                echo "<div class='success'><strong>composer.json created successfully!</strong></div>";
                echo "<p>Now run: <code>composer install</code> to install mPDF</p>";
            } else {
                echo "<p>✗ Failed to create composer.json</p>";
            }
            echo "</div>";
        }
        
        if ($action === 'download_dejavu') {
            echo "<div class='step'>";
            echo "<h3>Download DejaVu Fonts Information</h3>";
            echo "<p>Since FontForge might not be available, you can download pre-compiled DejaVu fonts:</p>";
            echo "<ol>";
            echo "<li>Visit: <a href='https://dejavu-fonts.github.io/' target='_blank'>https://dejavu-fonts.github.io/</a></li>";
            echo "<li>Download the latest TTF package</li>";
            echo "<li>Extract the following files to <code>fonts/dejavu-serif-ttf/</code>:</li>";
            echo "<ul>";
            echo "<li>DejaVuSerif.ttf</li>";
            echo "<li>DejaVuSerif-Bold.ttf</li>";
            echo "<li>DejaVuSerif-Italic.ttf</li>";
            echo "<li>DejaVuSerif-BoldItalic.ttf</li>";
            echo "</ul>";
            echo "</ol>";
            echo "</div>";
        }
        ?>
        
        <div class="step">
            <h3>Step 1: Create Font Directories</h3>
            <p>Create the necessary directories for font files.</p>
            <form method="get">
                <input type="hidden" name="action" value="create_directories">
                <button type="submit" class="button">Create Directories</button>
            </form>
        </div>
        
        <div class="step">
            <h3>Step 2: Setup Composer Configuration</h3>
            <p>Create composer.json file for mPDF installation.</p>
            <form method="get">
                <input type="hidden" name="action" value="create_composer">
                <button type="submit" class="button">Create composer.json</button>
            </form>
        </div>
        
        <div class="step">
            <h3>Step 3: Install mPDF</h3>
            <p>After creating composer.json, run this command in your terminal:</p>
            <div class="code">composer install</div>
            <p>Or if you don't have Composer installed globally:</p>
            <div class="code">php composer.phar install</div>
        </div>
        
        <div class="step">
            <h3>Step 4: Get DejaVu Fonts</h3>
            <p>You have two options:</p>
            <p><strong>Option A:</strong> Generate from source files (requires FontForge)</p>
            <a href="generate_dejavu_fonts.php" class="button">Generate DejaVu Fonts</a>
            
            <p><strong>Option B:</strong> Download pre-compiled fonts</p>
            <form method="get">
                <input type="hidden" name="action" value="download_dejavu">
                <button type="submit" class="button">Show Download Instructions</button>
            </form>
        </div>
        
        <div class="step">
            <h3>Step 5: Get Sakal Bharati Font</h3>
            <p>Download the Sakal Bharati font from CDAC:</p>
            <ol>
                <li>Visit: <a href="https://www.cdac.in/index.aspx?id=dl_sakal_bharati_font" target="_blank">CDAC Sakal Bharati Font</a></li>
                <li>Download the font package</li>
                <li>Extract the TTF file</li>
                <li>Rename it to <code>SakalBharati.ttf</code> (if needed)</li>
                <li>Place it in: <code>fonts/sakal-bharati/SakalBharati.ttf</code></li>
            </ol>
        </div>
        
        <div class="step">
            <h3>Step 6: Check Status</h3>
            <p>Check the installation status and test your fonts:</p>
            <a href="font_status.php" class="button success">Check Status</a>
            <a href="test_fonts.html" class="button">Test Fonts</a>
        </div>
        
        <div class="step warning">
            <h3>Current Status</h3>
            <?php
            $status = [];
            
            // Check directories
            if (is_dir('fonts/dejavu-serif-ttf')) $status[] = "✓ DejaVu directory exists";
            else $status[] = "✗ DejaVu directory missing";
            
            if (is_dir('fonts/sakal-bharati')) $status[] = "✓ Sakal Bharati directory exists";
            else $status[] = "✗ Sakal Bharati directory missing";
            
            // Check composer
            if (file_exists('composer.json')) $status[] = "✓ composer.json exists";
            else $status[] = "✗ composer.json missing";
            
            if (file_exists('vendor/autoload.php')) $status[] = "✓ mPDF installed";
            else $status[] = "✗ mPDF not installed";
            
            // Check fonts
            if (file_exists('fonts/Lohit-Bengali/Lohit-Bengali.ttf')) $status[] = "✓ Lohit Bengali font found";
            else $status[] = "✗ Lohit Bengali font missing";
            
            foreach ($status as $item) {
                echo "<p>$item</p>";
            }
            ?>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="PDF_FONT_SETUP_README.md" class="button">View Full Documentation</a>
        </div>
    </div>
</body>
</html>
