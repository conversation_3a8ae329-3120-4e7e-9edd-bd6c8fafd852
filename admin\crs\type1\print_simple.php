<?php
// Simple PDF generation using browser print functionality
include '../includes/config.php';

if(!isset($_GET['id'])){
    die("Certificate ID is required");
}

$searchid = mysqli_real_escape_string($conn,$_GET['id']);
$ecode = isset($_GET['cont']) ? mysqli_real_escape_string($conn,$_GET['cont']) : '';

mysqli_set_charset($conn,"utf8");
$a = mysqli_query($conn,"SELECT * FROM birthmanual2 Where id='".$searchid."'");
$b = mysqli_fetch_array($a);

if(!$b) {
    die("Certificate not found");
}

$hsptl = $b['hospital'];
mysqli_set_charset($conn,"utf8");
$c = mysqli_query($conn,"SELECT * FROM portal Where hospital='".$hsptl."'");
$f = mysqli_fetch_array($c);

if(!$f) {
    die("Portal configuration not found");
}

$ww = mysqli_query($conn,"SELECT * FROM website Where 1");
$w = mysqli_fetch_array($ww);
$wurl = isset($w['url']) ? $w['url'] : '';

// Set headers for PDF download
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birth Certificate - <?php echo htmlspecialchars($b['name']); ?></title>
    <style>
        @page {
            size: A4;
            margin: 10mm;
        }
        
        body {
            font-family: 'DejaVu Serif', 'Times New Roman', serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            color: #000;
        }
        
        .certificate-container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            border: 2px solid #000;
            padding: 5mm;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            width: 80px;
            height: auto;
            margin-bottom: 10px;
        }
        
        .govt-info {
            font-weight: bold;
            color: #0d0d57;
            margin-bottom: 10px;
        }
        
        .certificate-title {
            color: #076a07;
            font-size: 13px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .content-section {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        
        .left-column, .right-column {
            width: 48%;
        }
        
        .field {
            margin-bottom: 10px;
            font-size: 11px;
        }
        
        .field-label {
            font-weight: bold;
        }
        
        .signature-section {
            text-align: center;
            margin-top: 30px;
        }
        
        .signature-image {
            width: 60px;
            height: 50px;
            margin-bottom: 10px;
        }
        
        .footer-text {
            text-align: center;
            font-size: 10px;
            color: #0d0d57;
            margin-top: 20px;
        }
        
        .qr-code {
            width: 65px;
            height: 65px;
            margin: 10px 0;
        }
        
        .no-print {
            background: #f0f8ff;
            border: 1px solid #0066cc;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                font-size: 10px;
            }
            
            .certificate-container {
                border: 1px solid #000;
                page-break-inside: avoid;
            }
        }
        
        .download-button {
            background: #0066cc;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-button:hover {
            background: #0052a3;
        }
    </style>
    <script>
        function printCertificate() {
            window.print();
        }
        
        function downloadPDF() {
            // Hide the instructions
            document.querySelector('.no-print').style.display = 'none';
            
            // Trigger print dialog
            window.print();
            
            // Show instructions again after a delay
            setTimeout(function() {
                document.querySelector('.no-print').style.display = 'block';
            }, 1000);
        }
    </script>
</head>
<body>
    <div class="no-print">
        <h3>📄 Birth Certificate PDF Download</h3>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Click "Download as PDF" button below</li>
            <li>In the print dialog, select "Save as PDF" or "Microsoft Print to PDF"</li>
            <li>Choose your desired location and save the file</li>
        </ol>
        <button class="download-button" onclick="downloadPDF()">📥 Download as PDF</button>
        <button class="download-button" onclick="printCertificate()">🖨️ Print Certificate</button>
        <a href="javascript:history.back()" class="download-button">← Go Back</a>
    </div>

    <div class="certificate-container">
        <div class="header-section">
            <?php if(file_exists('2.PNG')): ?>
            <img src="2.PNG" class="logo" alt="Government Logo">
            <?php endif; ?>
            
            <div class="govt-info">
                <div><?php echo strtoupper($f['govtlocal'] ?? ''); ?></div>
                <div><?php echo strtoupper($f['govten'] ?? ''); ?></div>
                <div style="margin-top: 5px;"><?php echo strtoupper($f['deptlocal'] ?? ''); ?></div>
                <div><?php echo strtoupper($f['dept'] ?? ''); ?></div>
                <div style="margin-top: 5px;"><?php echo strtoupper($f['hospitalnamelocal'] ?? ''); ?></div>
                <div><?php echo strtoupper($f['hospitalname'] ?? ''); ?></div>
            </div>
            
            <div class="certificate-title">
                <?php echo strtoupper($f['birthlanglocal'] ?? ''); ?><br>
                BIRTH CERTIFICATE
            </div>
        </div>

        <div class="content-section">
            <div class="left-column">
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['namelocal'] ?? ''); ?> / NAME:</span><br>
                    <?php echo strtoupper($b['name']); ?>
                </div>
                
                <?php if(!empty($b['aadharno'])): ?>
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['aadharlocal'] ?? ''); ?> / AADHAR NO.:</span><br>
                    XXXXXXXX<?php echo substr($b['aadharno'], -4); ?>
                </div>
                <?php endif; ?>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['doblocal'] ?? ''); ?> / DATE OF BIRTH:</span><br>
                    <?php echo $b['dob']; ?><br>
                    <?php echo !empty($b['dobwords']) ? strtoupper($b['dobwords']) : ''; ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['mnamelocal'] ?? ''); ?> / NAME OF MOTHER:</span><br>
                    <?php echo strtoupper($b['mname']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['aadharlocal'] ?? ''); ?> / MOTHER'S AADHAAR NO:</span><br>
                    <?php echo !empty($b['maadhar']) ? 'XXXXXXXX' . substr($b['maadhar'], -4) : ''; ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['birthaddresslocal'] ?? ''); ?> / ADDRESS OF PARENTS AT THE TIME OF BIRTH:</span><br>
                    <?php echo strtoupper($b['birthaddress']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['regnolocal'] ?? ''); ?> / REGISTRATION NUMBER:</span><br>
                    <?php echo strtoupper($b['regno']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['remarkslocal'] ?? ''); ?> / REMARKS (IF ANY):</span><br>
                    ----
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['doilocal'] ?? ''); ?> / DATE OF ISSUE:</span><br>
                    <?php echo strtoupper($b['dateofissue']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label">UPDATED ON:</span><br>
                    <?php echo date("d-m-y H:i:s"); ?>
                </div>
                
                <img src="http://api.qrserver.com/v1/create-qr-code/?color=000000&bgcolor=FFFFFF&data=https://dc.crsorgi.gov.in.crs.certificate-verify.site/crs/verifycertificate.php%3Fid%3D<?php echo $searchid;?>%26cont%3DOqi8rh5RCI3198U8b0EfzxlC4%3D&qzone=1&margin=0&size=400x400&ecc=L" class="qr-code" alt="QR Code">
            </div>
            
            <div class="right-column">
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['genderlocal'] ?? ''); ?> / SEX:</span><br>
                    <?php echo strtoupper($b['gender']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['poblocal'] ?? ''); ?> / PLACE OF BIRTH:</span><br>
                    <?php echo strtoupper($b['placeofbirth']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['fnamelocal'] ?? ''); ?> / NAME OF FATHER:</span><br>
                    <?php echo strtoupper($b['fname']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['aadharlocal'] ?? ''); ?> / FATHER'S AADHAAR NO:</span><br>
                    <?php echo !empty($b['faadhar']) ? 'XXXXXXXX' . substr($b['faadhar'], -4) : ''; ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['permanentaddresslocal'] ?? ''); ?> / PERMANENT ADDRESS OF PARENTS:</span><br>
                    <?php echo strtoupper($b['permanentaddress']); ?>
                </div>
                
                <div class="field">
                    <span class="field-label"><?php echo strtoupper($f['regdatelocal'] ?? ''); ?> / DATE OF REGISTRATION:</span><br>
                    <?php echo strtoupper($b['dateofregister']); ?>
                </div>
                
                <div class="signature-section">
                    <?php if(isset($f['signimage']) && !empty($f['signimage'])): ?>
                    <img src="data:<?php echo $f['signimagetype'];?>;base64,<?php echo base64_encode($f['signimage'])?>" class="signature-image" alt="Signature">
                    <?php endif; ?>
                    
                    <div style="font-size: 10px;">
                        <strong><?php echo strtoupper($f['ialocal'] ?? ''); ?> / Issuing Authority:</strong><br>
                        <div style="color: #60F; font-weight: bold;">
                            <?php echo strtoupper($f['registrarlocal'] ?? ''); ?><br>
                            <?php echo strtoupper($f['registrar'] ?? ''); ?><br>
                            <?php echo strtoupper($f['reghospitallocal'] ?? ''); ?><br>
                            <?php echo strtoupper($f['reghospital'] ?? ''); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-text">
            <p>"THIS IS A COMPUTER GENERATED CERTIFICATE WHICH CONTAINS FACSIMILE SIGNATURE OF THE ISSUING AUTHORITY"</p>
            <p>"THE GOVT. OF INDIA VIDE CIRCULAR NO. 1/12/2014-VS(CRS) DATED 27-JULY-2015 HAS APPROVED THIS CERTIFICATE AS A VALID LEGAL DOCUMENT FOR ALL OFFICIAL PURPOSES".</p>
            <p style="color: darkgreen; font-weight: bold;">"<?php echo strtoupper($f['lastlinelocal'] ?? ''); ?>" / ENSURE REGISTRATION OF EVERY BIRTH AND DEATH</p>
        </div>
        
        <div style="text-align: center; margin-top: 10px;">
            <img src="https://crsorgo.org/code.png" style="width: 300px; height: 30px;" alt="Footer Image">
        </div>
    </div>
</body>
</html>
