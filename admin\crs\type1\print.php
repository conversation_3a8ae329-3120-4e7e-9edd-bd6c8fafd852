<?php
// Include the phpToPDF library
require("phpToPDF.php");
include '../includes/config.php';
error_reporting(0);

if(!isset($_GET['id'])){
    die("Certificate ID is required");
}

$searchid = mysqli_real_escape_string($conn,$_GET['id']);
$ecode = isset($_GET['cont']) ? mysqli_real_escape_string($conn,$_GET['cont']) : '';

mysqli_set_charset($conn,"utf8");
$a = mysqli_query($conn,"SELECT * FROM birthmanual2 Where id='".$searchid."'");
$b = mysqli_fetch_array($a);

if(!$b) {
    die("Certificate not found");
}

$hsptl = $b['hospital'];
mysqli_set_charset($conn,"utf8");
$c = mysqli_query($conn,"SELECT * FROM portal Where hospital='".$hsptl."'");
$f = mysqli_fetch_array($c);

if(!$f) {
    die("Portal configuration not found");
}

$ww = mysqli_query($conn,"SELECT * FROM website Where 1");
$w = mysqli_fetch_array($ww);
$wurl = isset($w['url']) ? $w['url'] : '';

// Generate the HTML content for the certificate
$logo_image = '';
if (file_exists('2.PNG')) {
    $logo_image = 'data:image/png;base64,' . base64_encode(file_get_contents('2.PNG'));
}

$my_html = '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Birth Certificate</title>
    <style>
        body {
            padding: 0.5em;
            font-family: Arial, sans-serif;
            font-size: 11px;
            margin: 0;
        }
        p { font-size: 11px; margin: 5px 0; }
        table { border-collapse: collapse; }
        .wrapper { min-height: 50vh; display: flex; flex-direction: column; }
        .content { display: flex; flex: 1; color: #000; }
        .columns { display: flex; flex:1; }
        .main { padding-left:40px; flex: 1; order: 2; }
        .sidebar-first { padding-left:50px; width: 20%; order: 1; }
        .sidebar-second { text-align:right; padding-right:50px; margin-top:-20px; width: 20%; order: 3; }
    </style>
</head>
<body>
    <div style="border:1px solid black; padding:2px;">
        <div style="border:2px solid black;">';

if ($logo_image) {
    $my_html .= '<center><img src="' . $logo_image . '" width="80px" style="padding-top: 5px;"></center>';
}

$my_html .= '
            <div class="wrapper">
                <section class="content">
                    <div class="columns">
                        <main class="main">
                            <b style="color:#0d0d57; text-align: center; font-weight: bold">
                                <p><b>' . strtoupper($f['govtlocal']) . '</b><br/>' . strtoupper($f['govten']) . '</p>
                                <p style="margin-top:-10px">' . strtoupper($f['deptlocal']) . '<br/>' . strtoupper($f['dept']) . '</p>
                                <p style="margin-top:-5px">' . strtoupper($f['hospitalnamelocal']) . '<br/>' . strtoupper($f['hospitalname']) . '<br/></p>
                                <p><b style="color:#076a07;font-size: 13px;margin-top:3px;">' . strtoupper($f['birthlanglocal']) . '<br/>BIRTH CERTIFICATE<br><br></b></p>
                            </b>
                        </main>

                        <aside class="sidebar-first">
                            <b style="font-size: 12px;padding:47px;font-weight: lighter;">' . (isset($f['topleftlocal']) ? $f['topleftlocal'] : '') . '</b><br>
                            <b style="font-size: 12px;padding:46px;font-weight: lighter;">No. 1</b><br/>';

if (isset($f['topleftimage']) && !empty($f['topleftimage'])) {
    $my_html .= '<img src="data:' . $f['topleftimagetype'] . ';base64,' . base64_encode($f['topleftimage']) . '" style="float: left; padding: 20px;margin-top:-15px" height="70px" width="70px;">';
}

$my_html .= '                        </aside>

                        <aside class="sidebar-second" style="padding:20px;padding-right:80px;">
                            <b style="font-size: 12px;padding:-10px;font-weight: lighter;text-align: right;">' . (isset($f['toprightlocal']) ? $f['toprightlocal'] : '') . '</b><br>
                            <b style="font-size: 12px;padding:-10px;font-weight: lighter;">FORM-5</b><br/>';

if (isset($f['toprightimage']) && !empty($f['toprightimage'])) {
    $my_html .= '<img src="data:' . $f['toprightimagetype'] . ';base64,' . base64_encode($f['toprightimage']) . '" style="float: right;" height="70px" width="70px;">';
}

$my_html .= '                        </aside>
                    </div>
                </section>
            </div>

            <div style="font-size:11px; margin-left: 10px;">
                <p>' . strtoupper($f['line1local']) . '<br/>' . strtoupper($f['line1']) . '</p>
                <p>' . strtoupper($f['line2local']) . '<br>' . strtoupper($f['line2']) . '</p>
            </div>

            <table style="padding-top: 1px; width:95%; margin-left: 10px;">
                <tr>
                    <td style="width: 55%; padding-top:20px">
                        <p>' . strtoupper($f['namelocal']) . ' / NAME : ' . strtoupper($b['name']) . '</p>';

if($b['aadharno'] != "") {
    $my_html .= '<p>' . strtoupper($f['aadharlocal']) . ' / AADHAR NO. :<br>XXXXXXXX' . substr($b['aadharno'], -4) . '</p>';
}

$my_html .= '
                        <p>' . strtoupper($f['doblocal']) . ' | DATE OF BIRTH:<br>' . $b['dob'] . '<br>';

if($b['dobwords'] != "") {
    $my_html .= strtoupper($b['dobwords']);
} else {
    $my_html .= '&nbsp;';
}

$my_html .= '</p>
                        <p>' . strtoupper($f['mnamelocal']) . ' / NAME OF MOTHER:<br/>' . strtoupper($b['mname']) . '</p>
                        <p>' . strtoupper($f['aadharlocal']) . ' / MOTHER\'S AADHAAR NO :<br/>';

if($b['maadhar'] != "") {
    $my_html .= 'XXXXXXXX' . substr($b['maadhar'], -4);
} else {
    $my_html .= '&nbsp;';
}

$my_html .= '</p>
                        <p style="height:57px">' . strtoupper($f['birthaddresslocal']) . ' / ADDRESS OF PARENTS AT THE TIME OF BIRTH OF THE CHILD :<br/>' . strtoupper($b['birthaddress']) . '</p>
                        <p>' . strtoupper($f['regnolocal']) . ' / REGISTRATION NUMBER:<br>' . strtoupper($b['regno']) . '</p>
                        <p>' . strtoupper($f['remarkslocal']) . ' / REMARKS (IF ANY):<br>----</p>
                        <p style="margin-top:40px">' . strtoupper($f['doilocal']) . ' / DATE OF ISSUE :<br/>' . strtoupper($b['dateofissue']) . '</p>
                        <br>
                        <p>UPDATED ON :<br/>' . date("d-m-y H:i:s") . '</p>
                        <img src="http://api.qrserver.com/v1/create-qr-code/?color=000000&bgcolor=FFFFFF&data=https://dc.crsorgi.gov.in.crs.certificate-verify.site/crs/verifycertificate.php%3Fid%3D' . $_GET['id'] . '%26cont%3DOqi8rh5RCI3198U8b0EfzxlC4%3D&qzone=1&margin=0&size=400x400&ecc=L" width="65px" height="65px" />
                    </td>
                    <td>
                        <div style="margin-top: -60px" width="140%">
                            <p>' . strtoupper($f['genderlocal']) . ' / SEX : ' . strtoupper($b['gender']) . '</p>
                            <p></p>';

if($b['aadharno'] != "") {
    $my_html .= '<p>&nbsp;<br>&nbsp;</p>';
}

$my_html .= '
                            <p style="height: 40px">' . strtoupper($f['poblocal']) . ' / PLACE OF BIRTH :<br>' . strtoupper($b['placeofbirth']) . '</p>
                            <p>' . strtoupper($f['fnamelocal']) . ' / NAME OF FATHER :<br/>' . strtoupper($b['fname']) . '</p>
                            <p>' . strtoupper($f['aadharlocal']) . ' / FATHER\'S AADHAAR NO:<br/>';

if($b['faadhar'] != "") {
    $my_html .= 'XXXXXXXX' . substr($b['faadhar'], -4);
} else {
    $my_html .= '&nbsp;';
}

$my_html .= '</p>
                            <p style="height:57px">' . strtoupper($f['permanentaddresslocal']) . ' / PERMANENT ADDRESS OF PARENTS:<br>' . strtoupper($b['permanentaddress']) . '</p>
                            <p>' . strtoupper($f['regdatelocal']) . ' / DATE OF REGISTRATION:<br/>' . strtoupper($b['dateofregister']) . '</p>
                            
                            <div style="text-align: center; margin-top:20px;">';

if (isset($f['signimage']) && !empty($f['signimage'])) {
    $my_html .= '<img src="data:' . $f['signimagetype'] . ';base64,' . base64_encode($f['signimage']) . '" width="60px" height="50px"><br />';
}

$my_html .= '                                <p style="font-size: 10px;">' . (isset($f['ialocal']) ? strtoupper($f['ialocal']) : '') . ' / Issuing Authority :<br>
                                    <div style="float: left; font-size: 12px;">
                                        <div style="color:#60F; font-weight: bold; text-align:center;">
                                            <span lang="hi">' . (isset($f['registrarlocal']) ? strtoupper($f['registrarlocal']) : '') . '<br></span>' . (isset($f['registrar']) ? strtoupper($f['registrar']) : '') . '<br>
                                            ' . (isset($f['reghospitallocal']) ? strtoupper($f['reghospitallocal']) : '') . '<br>' . (isset($f['reghospital']) ? strtoupper($f['reghospital']) : '') . '
                                        </div>
                                    </div>
                                </p>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <br/>
            <p style="margin:20px;color: #0d0d57 ;text-align: center;">"THIS IS A COMPUTER GENERATED CERTIFICATE WHICH CONTAINS FACSIMILE SIGNATURE OF THE ISSUING AUTHORITY"<br/> " THE GOVT. OF INDIA VIDE CIRCULAR NO. 1/12/2014-VS(CRS) DATED 27-JULY-2015 HAS <br/>APPROVED THIS CERTIFICATE AS A VALID LEGAL DOCUMENT FOR ALL OFFICIAL PURPOSES".</p>
            <b><p style="color: darkgreen; margin-left: 100px;">"' . strtoupper($f['lastlinelocal']) . '" / ENSURE REGISTRATION OF EVERY BIRTH AND DEATH "</p></b>

            <center>
                <img alt="" src="https://crsorgo.org/code.png" style="width:300px;height:30px">
            </center>
            <br/>
        </div>
    </div>
</body>
</html>';

// Set PDF options for download
$pdf_options = array(
    "source_type" => 'html',
    "source" => $my_html,
    "action" => 'download',
    "save_directory" => '',
    "file_name" => 'birth_certificate_' . $searchid . '.pdf',
    "page_size" => 'A4',
    "page_orientation" => 'portrait',
    "margin_top" => '10',
    "margin_bottom" => '10',
    "margin_left" => '10',
    "margin_right" => '10'
);

// Call the phpToPDF function to generate and download the PDF
try {
    phptopdf($pdf_options);
} catch (Exception $e) {
    // If PDF generation fails, redirect to fallback version
    $fallback_url = 'print_fallback.php?id=' . urlencode($searchid);
    if (!empty($ecode)) {
        $fallback_url .= '&cont=' . urlencode($ecode);
    }
    header('Location: ' . $fallback_url);
    exit;
}
?>
