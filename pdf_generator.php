<?php
/**
 * PDF Generator using mPDF with custom fonts
 * Supports Loh Bengali, DejaVu Serif, and Sakal Bharati fonts
 */

function generatePDF($options) {
    // Check if mPDF is available
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        return generateMPDF($options);
    } else {
        // Fallback to phpToPDF if mPDF is not available
        return generatePhpToPDF($options);
    }
}

function generateMPDF($options) {
    try {
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => $options['page_size'] ?? 'A4',
            'orientation' => $options['page_orientation'] ?? 'P',
            'margin_left' => $options['margin_left'] ?? 10,
            'margin_right' => $options['margin_right'] ?? 10,
            'margin_top' => $options['margin_top'] ?? 10,
            'margin_bottom' => $options['margin_bottom'] ?? 10,
            'fontDir' => [
                __DIR__ . '/fonts/dejavu-serif-ttf',
                __DIR__ . '/fonts/sakal-bharati',
                __DIR__ . '/fonts/loh-bengali'
            ],
            'fontdata' => [
                'dejavuserif' => [
                    'R' => 'DejaVuSerif.ttf',
                    'B' => 'DejaVuSerif-Bold.ttf',
                    'I' => 'DejaVuSerif-Italic.ttf',
                    'BI' => 'DejaVuSerif-BoldItalic.ttf'
                ],
                'sakalbharati' => [
                    'R' => 'SakalBharati.ttf'
                ],
                'lohbengali' => [
                    'R' => 'LohBengali.ttf'
                ]
            ],
            'default_font' => 'dejavuserif'
        ]);

        // Add custom CSS for fonts
        $css = '
        body, p, div, span, td, th {
            font-family: "lohbengali", "dejavuserif", "sakalbharati", serif;
        }
        ';
        
        $mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);
        $mpdf->WriteHTML($options['source']);

        $filename = $options['file_name'] ?? 'document.pdf';
        
        if (($options['action'] ?? 'download') === 'download') {
            $mpdf->Output($filename, \Mpdf\Output\Destination::DOWNLOAD);
        } else {
            $mpdf->Output($options['save_directory'] . $filename, \Mpdf\Output\Destination::FILE);
        }
        
        return true;
    } catch (Exception $e) {
        throw new Exception('mPDF Error: ' . $e->getMessage());
    }
}

function generatePhpToPDF($options) {
    // Fallback to phpToPDF
    if (function_exists('phptopdf')) {
        phptopdf($options);
        return true;
    } else {
        throw new Exception('No PDF generation library available');
    }
}

// Alternative simple PDF generation using TCPDF (if available)
function generateTCPDF($options) {
    if (class_exists('TCPDF')) {
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('Birth Certificate System');
        $pdf->SetTitle('Birth Certificate');
        
        // Set default header data
        $pdf->SetHeaderData('', 0, 'Birth Certificate', '');
        
        // Set header and footer fonts
        $pdf->setHeaderFont(Array('dejavuserif', '', 10));
        $pdf->setFooterFont(Array('dejavuserif', '', 8));
        
        // Set default monospaced font
        $pdf->SetDefaultMonospacedFont('dejavuserif');
        
        // Set margins
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        
        // Set auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);
        
        // Add a page
        $pdf->AddPage();
        
        // Write HTML content
        $pdf->writeHTML($options['source'], true, false, true, false, '');
        
        $filename = $options['file_name'] ?? 'document.pdf';
        
        if (($options['action'] ?? 'download') === 'download') {
            $pdf->Output($filename, 'D');
        } else {
            $pdf->Output($options['save_directory'] . $filename, 'F');
        }
        
        return true;
    } else {
        throw new Exception('TCPDF not available');
    }
}

// Browser-based PDF generation fallback
function generateBrowserPDF($options) {
    $filename = $options['file_name'] ?? 'document.pdf';
    $html = $options['source'];
    
    // Create a temporary HTML file for browser printing
    $temp_file = 'temp_' . uniqid() . '.html';
    
    $full_html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Birth Certificate</title>
        <style>
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
            }
            body {
                font-family: "DejaVu Serif", serif;
                font-size: 11px;
            }
        </style>
        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </head>
    <body>
        <div class="no-print" style="background: #f0f8ff; padding: 10px; margin-bottom: 20px; border: 1px solid #0066cc;">
            <h3>Print Instructions</h3>
            <p>1. Use Ctrl+P or Cmd+P to print</p>
            <p>2. Select "Save as PDF" in the print dialog</p>
            <p>3. Choose your desired location and save</p>
        </div>
        ' . $html . '
    </body>
    </html>';
    
    file_put_contents($temp_file, $full_html);
    
    // Redirect to the temporary file
    header('Location: ' . $temp_file);
    exit;
}
?>
