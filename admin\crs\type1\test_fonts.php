<?php
// Font Test File
include '../includes/config.php';
error_reporting(0);

// Test data
$test_hindi = "जन्म प्रमाण पत्र";
$test_english = "Birth Certificate";
$test_mixed = "Name / नाम : <PERSON>";
?>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&family=Noto+Sans:wght@400;700&family=Noto+Serif+Devanagari:wght@400;700&display=swap" rel="stylesheet">
    <title>Font Test</title>
    <style>
        body {
            padding: 20px;
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .font-test {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        
        .font-loh-bengali {
            font-family: 'Loh Bengali', serif;
        }
        
        .font-dejavu {
            font-family: 'DejaVu Serif', serif;
        }
        
        .font-sakal {
            font-family: 'Sakal Bharati', serif;
        }
        
        .font-combined {
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }
        
        .font-noto {
            font-family: 'Noto Sans Devanagari', 'Noto Sans', sans-serif;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 5px;
        }
        
        .test-button {
            background: #0066cc;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: #0052a3;
        }
    </style>
</head>
<body>
    <h1>Font Test for Birth Certificate PDF</h1>
    
    <div class="test-button">
        <a href="print.php?id=1&cont=test" class="test-button">Test Main PDF Generator</a>
        <a href="print_simple.php?id=1&cont=test" class="test-button">Test Simple PDF (Recommended)</a>
        <a href="print_fallback.php?id=1&cont=test" class="test-button">Test Fallback Version</a>
        <a href="debug_print.php?id=1&cont=test" class="test-button">Debug PDF Generation</a>
        <a href="../../../install.php" class="test-button">Font Installer</a>
    </div>
    
    <h2>Font Test Results</h2>
    
    <div class="font-test">
        <h3>Combined Font Stack (Recommended)</h3>
        <div class="font-combined">
            <p><strong>English:</strong> <?php echo $test_english; ?></p>
            <p><strong>Hindi:</strong> <?php echo $test_hindi; ?></p>
            <p><strong>Mixed:</strong> <?php echo $test_mixed; ?></p>
            <p><strong>Numbers:</strong> 1234567890</p>
            <p><strong>Special Characters:</strong> @#$%^&*()</p>
        </div>
    </div>
    
    <div class="font-test">
        <h3>Loh Bengali Font</h3>
        <div class="font-loh-bengali">
            <p><strong>English:</strong> <?php echo $test_english; ?></p>
            <p><strong>Hindi:</strong> <?php echo $test_hindi; ?></p>
            <p><strong>Mixed:</strong> <?php echo $test_mixed; ?></p>
        </div>
    </div>
    
    <div class="font-test">
        <h3>DejaVu Serif Font</h3>
        <div class="font-dejavu">
            <p><strong>English:</strong> <?php echo $test_english; ?></p>
            <p><strong>Hindi:</strong> <?php echo $test_hindi; ?></p>
            <p><strong>Mixed:</strong> <?php echo $test_mixed; ?></p>
        </div>
    </div>
    
    <div class="font-test">
        <h3>Sakal Bharati Font</h3>
        <div class="font-sakal">
            <p><strong>English:</strong> <?php echo $test_english; ?></p>
            <p><strong>Hindi:</strong> <?php echo $test_hindi; ?></p>
            <p><strong>Mixed:</strong> <?php echo $test_mixed; ?></p>
        </div>
    </div>
    
    <div class="font-test">
        <h3>Noto Fonts (Fallback)</h3>
        <div class="font-noto">
            <p><strong>English:</strong> <?php echo $test_english; ?></p>
            <p><strong>Hindi:</strong> <?php echo $test_hindi; ?></p>
            <p><strong>Mixed:</strong> <?php echo $test_mixed; ?></p>
        </div>
    </div>
    
    <h2>Font Installation Status</h2>
    <div class="font-test">
        <?php
        $font_status = [];
        
        // Check for font directories
        if (is_dir('../../../fonts/dejavu-serif-ttf')) {
            $font_status[] = "✓ DejaVu Serif directory exists";
        } else {
            $font_status[] = "✗ DejaVu Serif directory missing";
        }
        
        if (is_dir('../../../fonts/sakal-bharati')) {
            $font_status[] = "✓ Sakal Bharati directory exists";
        } else {
            $font_status[] = "✗ Sakal Bharati directory missing";
        }
        
        if (is_dir('../../../fonts/Lohit-Bengali')) {
            $font_status[] = "✓ Lohit Bengali directory exists";
        } else {
            $font_status[] = "✗ Lohit Bengali directory missing";
        }
        
        // Check for specific font files
        if (file_exists('../../../fonts/dejavu-serif-ttf/DejaVuSerif.ttf')) {
            $font_status[] = "✓ DejaVu Serif TTF file found";
        } else {
            $font_status[] = "✗ DejaVu Serif TTF file missing";
        }
        
        if (file_exists('../../../fonts/sakal-bharati/SakalBharati.ttf')) {
            $font_status[] = "✓ Sakal Bharati TTF file found";
        } else {
            $font_status[] = "✗ Sakal Bharati TTF file missing";
        }
        
        foreach ($font_status as $status) {
            echo "<p>$status</p>";
        }
        ?>
    </div>
    
    <h2>Instructions</h2>
    <div class="font-test">
        <p><strong>To properly use these fonts in PDF generation:</strong></p>
        <ol>
            <li>Install the required font files using the <a href="../../../install.php">Font Installer</a></li>
            <li>Download and place the font files in the correct directories</li>
            <li>Test the PDF generation using the buttons above</li>
            <li>If fonts don't display correctly, the system will fall back to web-safe fonts</li>
        </ol>
        
        <p><strong>Font Priority Order:</strong></p>
        <ol>
            <li>Loh Bengali (for Bengali/Hindi text)</li>
            <li>DejaVu Serif (for English and special characters)</li>
            <li>Sakal Bharati (for Devanagari script)</li>
            <li>DejaVu Serif 1 (fallback)</li>
            <li>Generic serif (final fallback)</li>
        </ol>
    </div>
    
    <div style="text-align: center; margin-top: 30px;">
        <a href="javascript:history.back()" class="test-button">Go Back</a>
    </div>
</body>
</html>
