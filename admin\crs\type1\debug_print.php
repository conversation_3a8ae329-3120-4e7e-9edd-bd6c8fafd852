<?php
// Debug version of print.php to capture errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the phpToPDF library
require("phpToPDF.php");
include '../includes/config.php';

echo "<h2>PDF Generation Debug</h2>";

if(!isset($_GET['id'])){
    die("Certificate ID is required");
}

$searchid = mysqli_real_escape_string($conn,$_GET['id']);
$ecode = isset($_GET['cont']) ? mysqli_real_escape_string($conn,$_GET['cont']) : '';

echo "<p>Certificate ID: " . htmlspecialchars($searchid) . "</p>";

mysqli_set_charset($conn,"utf8");
$a = mysqli_query($conn,"SELECT * FROM birthmanual2 Where id='".$searchid."'");
$b = mysqli_fetch_array($a);

if(!$b) {
    die("Certificate not found");
}

echo "<p>✓ Certificate data found</p>";

$hsptl = $b['hospital'];
mysqli_set_charset($conn,"utf8");
$c = mysqli_query($conn,"SELECT * FROM portal Where hospital='".$hsptl."'");
$f = mysqli_fetch_array($c);

if(!$f) {
    die("Portal configuration not found");
}

echo "<p>✓ Portal configuration found</p>";

$ww = mysqli_query($conn,"SELECT * FROM website Where 1");
$w = mysqli_fetch_array($ww);
$wurl = isset($w['url']) ? $w['url'] : '';

echo "<p>✓ Website configuration loaded</p>";

// Generate a simple HTML content for testing
$my_html = '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Birth Certificate Test</title>
    <style>
        body {
            font-family: "DejaVu Serif", serif;
            font-size: 12px;
            margin: 20px;
        }
    </style>
</head>
<body>
    <h1>Birth Certificate Test</h1>
    <p>Name: ' . htmlspecialchars($b['name']) . '</p>
    <p>Date of Birth: ' . htmlspecialchars($b['dob']) . '</p>
    <p>Father Name: ' . htmlspecialchars($b['fname']) . '</p>
    <p>Mother Name: ' . htmlspecialchars($b['mname']) . '</p>
    <p>Registration Number: ' . htmlspecialchars($b['regno']) . '</p>
    <p>Generated on: ' . date('Y-m-d H:i:s') . '</p>
</body>
</html>';

echo "<p>✓ HTML content generated</p>";

// Set PDF options for download
$pdf_options = array(
    "source_type" => 'html',
    "source" => $my_html,
    "action" => 'download',
    "save_directory" => '',
    "file_name" => 'test_certificate_' . $searchid . '.pdf',
    "page_size" => 'A4',
    "page_orientation" => 'portrait'
);

echo "<p>✓ PDF options configured</p>";
echo "<p>Attempting PDF generation...</p>";

// Call the phpToPDF function to generate and download the PDF
try {
    echo "<p>Calling phptopdf function...</p>";
    phptopdf($pdf_options);
    echo "<p>✓ PDF generation successful</p>";
} catch (Exception $e) {
    echo "<h3>❌ PDF Generation Error:</h3>";
    echo "<p>Error message: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Error file: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Error line: " . $e->getLine() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    
    echo "<h3>Fallback Options:</h3>";
    echo "<p><a href='print_fallback.php?id=" . urlencode($searchid) . "&cont=" . urlencode($ecode) . "'>Try Fallback Version</a></p>";
    echo "<p><a href='test_fonts.php'>Test Fonts</a></p>";
}
?>
