<?php
// Fallback PDF generation using browser print
include '../includes/config.php';
error_reporting(0);

if(!isset($_GET['id'])){
    die("Certificate ID is required");
}

$searchid = mysqli_real_escape_string($conn,$_GET['id']);
$ecode = isset($_GET['cont']) ? mysqli_real_escape_string($conn,$_GET['cont']) : '';

mysqli_set_charset($conn,"utf8");
$a = mysqli_query($conn,"SELECT * FROM birthmanual2 Where id='".$searchid."'");
$b = mysqli_fetch_array($a);

if(!$b) {
    die("Certificate not found");
}

$hsptl = $b['hospital'];
mysqli_set_charset($conn,"utf8");
$c = mysqli_query($conn,"SELECT * FROM portal Where hospital='".$hsptl."'");
$f = mysqli_fetch_array($c);

if(!$f) {
    die("Portal configuration not found");
}

$ww = mysqli_query($conn,"SELECT * FROM website Where 1");
$w = mysqli_fetch_array($ww);
$wurl = isset($w['url']) ? $w['url'] : '';
?>

<html>
<head>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&family=Noto+Sans:wght@400;700&family=Noto+Serif+Devanagari:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <title>Birth Certificate - Download</title>
    <style>
        body {
            padding: 0.5em;
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
            font-size: 11px;
        }
        p {
            font-size: 11px;
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }
        
        @media print {
            body {
                width: 21cm;
                height: 29.7cm;
                margin-bottom: 0mm;
                font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
            }
            .no-print { display: none; }
        }
        
        .wrapper { min-height: 50vh; display: flex; flex-direction: column; }
        .header, .footer { height: 50px; color: #fff; }
        .content { display: flex; flex: 1; color: #000; }
        .columns { display: flex; flex:1; }
        .main {
            padding-left:40px;
            flex: 1;
            order: 2;
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }
        .sidebar-first {
            padding-left:50px;
            width: 20%;
            order: 1;
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }
        .sidebar-second {
            text-align:right;
            padding-right:50px;
            margin-top:-20px;
            width: 20%;
            order: 3;
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }

        b, strong {
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }

        table {
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }

        td {
            font-family: 'Loh Bengali', 'DejaVu Serif', 'Sakal Bharati', 'DejaVu Serif 1', serif;
        }
        
        .download-instructions {
            background: #f0f8ff;
            border: 1px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .download-button {
            background: #0066cc;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .download-button:hover {
            background: #0052a3;
        }
    </style>
</head>
<body>
    <div class="no-print download-instructions">
        <h3>Download Birth Certificate as PDF</h3>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Click the "Print/Save as PDF" button below</li>
            <li>In the print dialog, select "Save as PDF" or "Microsoft Print to PDF"</li>
            <li>Choose your desired location and save the file</li>
        </ol>
        <button class="download-button" onclick="window.print()">Print/Save as PDF</button>
        <button class="download-button" onclick="window.history.back()">Go Back</button>
    </div>
    
    <div style="border:1px solid black; padding:2px;">
        <div style="border:2px solid black;">
            <?php if(file_exists('2.PNG')): ?>
            <center><img src="2.PNG" width="80px" style="padding-top: 5px;"></center>
            <?php endif; ?>
            
            <div class="wrapper">
                <section class="content">
                    <div class="columns">
                        <main class="main">
                            <b style="color:#0d0d57; text-align: center; font-weight: bold">
                                <p><b><?php echo strtoupper($f['govtlocal'])?></b><br/><?php echo strtoupper($f['govten'])?></p>
                                <p style="margin-top:-10px"><?php echo strtoupper($f['deptlocal'])?><br/><?php echo strtoupper($f['dept'])?></p>
                                <p style="margin-top:-5px"><?php echo strtoupper($f['hospitalnamelocal'])?><br/><?php echo strtoupper($f['hospitalname'])?><br/></p>
                                <p><b style="color:#076a07;font-size: 13px;margin-top:3px;"><?php echo strtoupper($f['birthlanglocal'])?><br/>BIRTH CERTIFICATE<br><br></b></p>
                            </b>
                        </main>
                        
                        <aside class="sidebar-first">
                            <b style="font-size: 12px;padding:47px;font-weight: lighter;"><?php echo $f['topleftlocal']; ?></b><br>
                            <b style="font-size: 12px;padding:46px;font-weight: lighter;">No. 1</b><br/>
                            <?php if(isset($f['topleftimage']) && !empty($f['topleftimage'])): ?>
                            <img src="data:<?php echo $f['topleftimagetype'];?>;base64,<?php echo base64_encode($f['topleftimage'])?>" style="float: left; padding: 20px;margin-top:-15px" height="70px" width="70px;">
                            <?php endif; ?>
                        </aside>
                        
                        <aside class="sidebar-second" style="padding:20px;padding-right:80px;">
                            <b style="font-size: 12px;padding:-10px;font-weight: lighter;text-align: right;"><?php echo $f['toprightlocal']; ?></b><br>
                            <b style="font-size: 12px;padding:-10px;font-weight: lighter;">FORM-5</b><br/>
                            <?php if(isset($f['toprightimage']) && !empty($f['toprightimage'])): ?>
                            <img src="data:<?php echo $f['toprightimagetype'];?>;base64,<?php echo base64_encode($f['toprightimage'])?>" style="float: right;" height="70px" width="70px;">
                            <?php endif; ?>
                        </aside>
                    </div>
                </section>
            </div>
            
            <div style="font-size:11px; margin-left: 10px;">
                <p><?php echo strtoupper($f['line1local'])?><br/><?php echo strtoupper($f['line1'])?></p>
                <p><?php echo strtoupper($f['line2local'])?><br><?php echo strtoupper($f['line2'])?></p>
            </div>
            
            <table style="padding-top: 1px; width:95%; margin-left: 10px;">
                <tr>
                    <td style="width: 55%; padding-top:20px">
                        <p><?php echo strtoupper($f['namelocal'])?> / NAME : <?php echo strtoupper($b['name']); ?></p>
                        <?php if($b['aadharno'] != ""): ?>
                        <p><?php echo strtoupper($f['aadharlocal'])?> / AADHAR NO. :<br>XXXXXXXX<?php echo substr($b['aadharno'], -4); ?></p>
                        <?php endif; ?>
                        <p><?php echo strtoupper($f['doblocal'])?> | DATE OF BIRTH:<br><?php echo $b['dob']; ?><br><?php echo ($b['dobwords'] != "") ? strtoupper($b['dobwords']) : '&nbsp;'; ?></p>
                        <p><?php echo strtoupper($f['mnamelocal'])?> / NAME OF MOTHER:<br/><?php echo strtoupper($b['mname']); ?></p>
                        <p><?php echo strtoupper($f['aadharlocal'])?> / MOTHER'S AADHAAR NO :<br/><?php echo ($b['maadhar'] != "") ? 'XXXXXXXX'.substr($b['maadhar'], -4) : '&nbsp;'; ?></p>
                        <p style="height:57px"><?php echo strtoupper($f['birthaddresslocal'])?> / ADDRESS OF PARENTS AT THE TIME OF BIRTH OF THE CHILD :<br/><?php echo strtoupper($b['birthaddress']); ?></p>
                        <p><?php echo strtoupper($f['regnolocal'])?> / REGISTRATION NUMBER:<br><?php echo strtoupper($b['regno']); ?></p>
                        <p><?php echo strtoupper($f['remarkslocal'])?> / REMARKS (IF ANY):<br>----</p>
                        <p style="margin-top:40px"><?php echo strtoupper($f['doilocal'])?> / DATE OF ISSUE :<br/><?php echo strtoupper($b['dateofissue']); ?></p>
                        <br>
                        <p>UPDATED ON :<br/><?php echo date("d-m-y H:i:s"); ?></p>
                        <img src="http://api.qrserver.com/v1/create-qr-code/?color=000000&bgcolor=FFFFFF&data=https://dc.crsorgi.gov.in.crs.certificate-verify.site/crs/verifycertificate.php%3Fid%3D<?php echo $_GET['id'];?>%26cont%3DOqi8rh5RCI3198U8b0EfzxlC4%3D&qzone=1&margin=0&size=400x400&ecc=L" width="65px" height="65px" />
                    </td>
                    <td>
                        <div style="margin-top: -60px" width="140%">
                            <p><?php echo strtoupper($f['genderlocal'])?> / SEX : <?php echo strtoupper($b['gender']); ?></p>
                            <p></p>
                            <?php if($b['aadharno'] != ""): ?>
                            <p>&nbsp;<br>&nbsp;</p>
                            <?php endif; ?>
                            <p style="height: 40px"><?php echo strtoupper($f['poblocal'])?> / PLACE OF BIRTH :<br><?php echo strtoupper($b['placeofbirth']); ?></p>
                            <p><?php echo strtoupper($f['fnamelocal'])?> / NAME OF FATHER :<br/><?php echo strtoupper($b['fname']); ?></p>
                            <p><?php echo strtoupper($f['aadharlocal'])?> / FATHER'S AADHAAR NO:<br/><?php echo ($b['faadhar'] != "") ? 'XXXXXXXX'.substr($b['faadhar'], -4) : '&nbsp;'; ?></p>
                            <p style="height:57px"><?php echo strtoupper($f['permanentaddresslocal'])?> / PERMANENT ADDRESS OF PARENTS:<br><?php echo strtoupper($b['permanentaddress']); ?></p>
                            <p><?php echo strtoupper($f['regdatelocal'])?> / DATE OF REGISTRATION:<br/><?php echo strtoupper($b['dateofregister']); ?></p>
                            
                            <div style="text-align: center; margin-top:20px;">
                                <?php if(isset($f['signimage']) && !empty($f['signimage'])): ?>
                                <img src="data:<?php echo $f['signimagetype'];?>;base64,<?php echo base64_encode($f['signimage'])?>" width="60px" height="50px"><br />
                                <?php endif; ?>
                                <p style="font-size: 10px;"><?php echo strtoupper($f['ialocal'])?> / Issuing Authority :<br>
                                    <div style="float: left; font-size: 12px;">
                                        <div style="color:#60F; font-weight: bold; text-align:center;">
                                            <span lang="hi"><?php echo strtoupper($f['registrarlocal'])?><br></span><?php echo strtoupper($f['registrar'])?><br>
                                            <?php echo strtoupper($f['reghospitallocal'])?><br><?php echo strtoupper($f['reghospital'])?>
                                        </div>
                                    </div>
                                </p>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <br/>
            <p style="margin:20px;color: #0d0d57 ;text-align: center;">"THIS IS A COMPUTER GENERATED CERTIFICATE WHICH CONTAINS FACSIMILE SIGNATURE OF THE ISSUING AUTHORITY"<br/> " THE GOVT. OF INDIA VIDE CIRCULAR NO. 1/12/2014-VS(CRS) DATED 27-JULY-2015 HAS <br/>APPROVED THIS CERTIFICATE AS A VALID LEGAL DOCUMENT FOR ALL OFFICIAL PURPOSES".</p>
            <b><p style="color: darkgreen; margin-left: 100px;">"<?php echo strtoupper($f['lastlinelocal'])?>" / ENSURE REGISTRATION OF EVERY BIRTH AND DEATH "</p></b>
            
            <center>
                <img alt="" src="https://crsorgo.org/code.png" style="width:300px;height:30px">
            </center>
            <br/>
        </div>
    </div>
</body>
</html>
